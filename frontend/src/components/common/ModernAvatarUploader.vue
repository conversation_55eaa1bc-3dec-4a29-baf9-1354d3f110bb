<template>
  <div class="modern-avatar-uploader">
    <!-- 头像显示区域 -->
    <div class="avatar-display" @click="triggerFileSelect">
      <div class="avatar-container">
        <img
          v-if="currentAvatar"
          :src="currentAvatar"
          :alt="altText"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder">
          <el-icon :size="32">
            <User />
          </el-icon>
          <span class="placeholder-text">{{ placeholderText }}</span>
        </div>

        <!-- 悬停遮罩 -->
        <div class="avatar-overlay">
          <el-icon :size="20">
            <Camera />
          </el-icon>
          <span class="overlay-text">{{ overlayText }}</span>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 现代化裁剪对话框 -->
    <el-dialog
      v-model="cropDialogVisible"
      :title="dialogTitle"
      width="900px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="modern-crop-dialog"
      @close="handleDialogClose"
    >
      <div class="crop-container">
        <!-- 左侧裁剪区域 -->
        <div class="crop-section">
          <div class="crop-header">
            <h4>裁剪头像</h4>
            <p>拖拽调整裁剪区域，获得最佳效果</p>
          </div>
          
          <div class="crop-area">
            <canvas
              ref="cropCanvasRef"
              class="crop-canvas"
              @mousedown="startCrop"
              @mousemove="updateCrop"
              @mouseup="endCrop"
              @touchstart="startCrop"
              @touchmove="updateCrop"
              @touchend="endCrop"
            ></canvas>
          </div>

          <!-- 裁剪控制工具 -->
          <div class="crop-tools">
            <div class="tool-group">
              <label class="tool-label">缩放</label>
              <el-slider
                v-model="cropState.scale"
                :min="0.5"
                :max="3"
                :step="0.1"
                :show-tooltip="false"
                @input="updateScale"
                class="scale-slider"
              />
            </div>
            
            <div class="tool-group">
              <label class="tool-label">旋转</label>
              <div class="rotation-controls">
                <el-button
                  size="small"
                  @click="rotateImage(-90)"
                  :icon="RefreshLeft"
                  circle
                />
                <el-button
                  size="small"
                  @click="rotateImage(90)"
                  :icon="RefreshRight"
                  circle
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧预览区域 -->
        <div class="preview-section">
          <div class="preview-header">
            <h4>预览效果</h4>
            <p>查看不同尺寸下的显示效果</p>
          </div>

          <div class="preview-grid">
            <div class="preview-item">
              <div class="preview-circle large">
                <canvas ref="previewLargeRef" width="120" height="120"></canvas>
              </div>
              <span class="preview-label">大头像</span>
              <span class="preview-size">120×120px</span>
            </div>

            <div class="preview-item">
              <div class="preview-circle medium">
                <canvas ref="previewMediumRef" width="80" height="80"></canvas>
              </div>
              <span class="preview-label">中头像</span>
              <span class="preview-size">80×80px</span>
            </div>

            <div class="preview-item">
              <div class="preview-circle small">
                <canvas ref="previewSmallRef" width="40" height="40"></canvas>
              </div>
              <span class="preview-label">小头像</span>
              <span class="preview-size">40×40px</span>
            </div>
          </div>

          <!-- 质量设置 -->
          <div class="quality-settings">
            <div class="setting-item">
              <label class="setting-label">输出质量</label>
              <el-select v-model="outputQuality" size="small">
                <el-option label="高质量 (90%)" :value="0.9" />
                <el-option label="标准 (80%)" :value="0.8" />
                <el-option label="压缩 (70%)" :value="0.7" />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">输出格式</label>
              <el-select v-model="outputFormat" size="small">
                <el-option label="JPEG" value="image/jpeg" />
                <el-option label="PNG" value="image/png" />
                <el-option label="WebP" value="image/webp" />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-info">
            <el-icon><InfoFilled /></el-icon>
            <span>建议上传正方形图片，获得最佳显示效果</span>
          </div>
          
          <div class="footer-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button @click="resetCrop" :icon="RefreshRight">重置</el-button>
            <el-button 
              type="primary" 
              @click="handleConfirm" 
              :loading="processing"
              :icon="Check"
            >
              {{ processing ? "处理中..." : "确认上传" }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import {
  User,
  Camera,
  RefreshLeft,
  RefreshRight,
  Check,
  InfoFilled,
} from "@element-plus/icons-vue";

// Props定义
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  avatarSize: {
    type: Number,
    default: 100,
  },
  outputSize: {
    type: Number,
    default: 200,
  },
  outputQuality: {
    type: Number,
    default: 0.9,
  },
  outputFormat: {
    type: String,
    default: "image/jpeg",
  },
  maxFileSize: {
    type: Number,
    default: 5 * 1024 * 1024, // 5MB
  },
  placeholderText: {
    type: String,
    default: "点击上传头像",
  },
  overlayText: {
    type: String,
    default: "更换头像",
  },
  dialogTitle: {
    type: String,
    default: "裁剪头像",
  },
  altText: {
    type: String,
    default: "用户头像",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// Emits定义
const emit = defineEmits([
  "update:modelValue",
  "file-select",
  "crop-complete",
  "crop-cancel",
]);

// 响应式数据
const fileInputRef = ref(null);
const cropCanvasRef = ref(null);
const previewLargeRef = ref(null);
const previewMediumRef = ref(null);
const previewSmallRef = ref(null);

const currentAvatar = ref(props.modelValue);
const cropDialogVisible = ref(false);
const originalImageUrl = ref("");
const processing = ref(false);

// 输出设置
const outputQuality = ref(props.outputQuality);
const outputFormat = ref(props.outputFormat);

// 裁剪状态
const cropState = reactive({
  isDragging: false,
  isResizing: false,
  resizeType: "",
  startX: 0,
  startY: 0,
  cropX: 50,
  cropY: 50,
  cropWidth: 200,
  cropHeight: 200,
  scale: 1,
  rotation: 0,
});

// 图片相关变量
let originalImage = null;
let canvasContext = null;

// 触发文件选择
const triggerFileSelect = () => {
  if (props.disabled) return;
  fileInputRef.value?.click();
};

// 文件选择处理
const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith("image/")) {
    ElMessage.error("请选择图片文件");
    return;
  }

  // 验证文件大小
  if (file.size > props.maxFileSize) {
    const maxSizeMB = (props.maxFileSize / (1024 * 1024)).toFixed(1);
    ElMessage.error(`文件大小不能超过 ${maxSizeMB}MB`);
    return;
  }

  // 读取文件并显示裁剪对话框
  const reader = new FileReader();
  reader.onload = (e) => {
    originalImageUrl.value = e.target.result;
    cropDialogVisible.value = true;

    nextTick(() => {
      initCropper();
    });
  };
  reader.onerror = () => {
    ElMessage.error("文件读取失败");
  };
  reader.readAsDataURL(file);

  // 触发文件选择事件
  emit("file-select", file);

  // 清空input值
  event.target.value = "";
};

// 初始化裁剪器
const initCropper = () => {
  if (!cropCanvasRef.value || !originalImageUrl.value) return;

  const canvas = cropCanvasRef.value;
  const ctx = canvas.getContext("2d");
  canvasContext = ctx;

  // 创建图片对象
  originalImage = new Image();
  originalImage.onload = () => {
    // 设置canvas尺寸
    const maxSize = 400;
    const scale = Math.min(maxSize / originalImage.width, maxSize / originalImage.height);
    
    canvas.width = originalImage.width * scale;
    canvas.height = originalImage.height * scale;

    // 初始化裁剪区域
    const size = Math.min(canvas.width, canvas.height) * 0.6;
    cropState.cropX = (canvas.width - size) / 2;
    cropState.cropY = (canvas.height - size) / 2;
    cropState.cropWidth = size;
    cropState.cropHeight = size;

    drawCanvas();
    updatePreviews();
  };
  originalImage.src = originalImageUrl.value;
};

// 绘制canvas
const drawCanvas = () => {
  if (!canvasContext || !originalImage) return;

  const canvas = cropCanvasRef.value;
  const ctx = canvasContext;

  // 清空canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 保存上下文状态
  ctx.save();

  // 应用变换
  const centerX = canvas.width / 2;
  const centerY = canvas.height / 2;
  
  ctx.translate(centerX, centerY);
  ctx.rotate((cropState.rotation * Math.PI) / 180);
  ctx.scale(cropState.scale, cropState.scale);
  ctx.translate(-centerX, -centerY);

  // 绘制图片
  ctx.drawImage(originalImage, 0, 0, canvas.width, canvas.height);

  // 恢复上下文状态
  ctx.restore();

  // 绘制裁剪框
  drawCropBox();
};

// 绘制裁剪框
const drawCropBox = () => {
  if (!canvasContext) return;

  const ctx = canvasContext;
  const { cropX, cropY, cropWidth, cropHeight } = cropState;

  // 绘制遮罩
  ctx.save();
  ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
  ctx.fillRect(0, 0, cropCanvasRef.value.width, cropCanvasRef.value.height);
  
  // 清除裁剪区域
  ctx.globalCompositeOperation = "destination-out";
  ctx.fillRect(cropX, cropY, cropWidth, cropHeight);
  
  ctx.restore();

  // 绘制裁剪框边框
  ctx.strokeStyle = "#409eff";
  ctx.lineWidth = 2;
  ctx.strokeRect(cropX, cropY, cropWidth, cropHeight);

  // 绘制控制点
  drawControlPoints();
};

// 绘制控制点
const drawControlPoints = () => {
  if (!canvasContext) return;

  const ctx = canvasContext;
  const { cropX, cropY, cropWidth, cropHeight } = cropState;
  const pointSize = 8;

  ctx.fillStyle = "#409eff";
  ctx.strokeStyle = "#ffffff";
  ctx.lineWidth = 2;

  // 四个角的控制点
  const points = [
    [cropX, cropY], // 左上
    [cropX + cropWidth, cropY], // 右上
    [cropX, cropY + cropHeight], // 左下
    [cropX + cropWidth, cropY + cropHeight], // 右下
  ];

  points.forEach(([x, y]) => {
    ctx.beginPath();
    ctx.arc(x, y, pointSize / 2, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
  });
};

// 更新预览
const updatePreviews = () => {
  if (!originalImage || !canvasContext) return;

  const previews = [
    { ref: previewLargeRef, size: 120 },
    { ref: previewMediumRef, size: 80 },
    { ref: previewSmallRef, size: 40 },
  ];

  previews.forEach(({ ref, size }) => {
    if (!ref.value) return;

    const canvas = ref.value;
    const ctx = canvas.getContext("2d");
    
    canvas.width = size;
    canvas.height = size;

    // 计算裁剪区域在原图中的位置
    const scaleX = originalImage.width / cropCanvasRef.value.width;
    const scaleY = originalImage.height / cropCanvasRef.value.height;
    
    const sourceX = cropState.cropX * scaleX;
    const sourceY = cropState.cropY * scaleY;
    const sourceWidth = cropState.cropWidth * scaleX;
    const sourceHeight = cropState.cropHeight * scaleY;

    // 绘制裁剪后的图片
    ctx.drawImage(
      originalImage,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      0,
      0,
      size,
      size
    );
  });
};

// 缩放更新
const updateScale = () => {
  drawCanvas();
  updatePreviews();
};

// 旋转图片
const rotateImage = (angle) => {
  cropState.rotation += angle;
  drawCanvas();
  updatePreviews();
};

// 重置裁剪
const resetCrop = () => {
  if (!cropCanvasRef.value) return;

  const canvas = cropCanvasRef.value;
  const size = Math.min(canvas.width, canvas.height) * 0.6;
  
  cropState.cropX = (canvas.width - size) / 2;
  cropState.cropY = (canvas.height - size) / 2;
  cropState.cropWidth = size;
  cropState.cropHeight = size;
  cropState.scale = 1;
  cropState.rotation = 0;

  drawCanvas();
  updatePreviews();
};

// 确认裁剪
const handleConfirm = async () => {
  try {
    processing.value = true;

    // 创建输出canvas
    const outputCanvas = document.createElement("canvas");
    const outputCtx = outputCanvas.getContext("2d");
    
    outputCanvas.width = props.outputSize;
    outputCanvas.height = props.outputSize;

    // 计算裁剪区域在原图中的位置
    const scaleX = originalImage.width / cropCanvasRef.value.width;
    const scaleY = originalImage.height / cropCanvasRef.value.height;
    
    const sourceX = cropState.cropX * scaleX;
    const sourceY = cropState.cropY * scaleY;
    const sourceWidth = cropState.cropWidth * scaleX;
    const sourceHeight = cropState.cropHeight * scaleY;

    // 绘制裁剪后的图片
    outputCtx.drawImage(
      originalImage,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      0,
      0,
      props.outputSize,
      props.outputSize
    );

    // 转换为Blob
    const blob = await new Promise((resolve, reject) => {
      outputCanvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("图片处理失败"));
          }
        },
        outputFormat.value,
        outputQuality.value
      );
    });

    // 生成预览URL
    const previewUrl = URL.createObjectURL(blob);
    currentAvatar.value = previewUrl;

    // 触发事件
    emit("update:modelValue", previewUrl);
    emit("crop-complete", {
      blob,
      canvas: outputCanvas,
      previewUrl,
      file: blob,
      size: blob.size,
    });

    cropDialogVisible.value = false;
    ElMessage.success("头像裁剪完成！");
  } catch (error) {
    console.error("裁剪失败:", error);
    ElMessage.error("裁剪失败: " + error.message);
  } finally {
    processing.value = false;
  }
};

// 取消裁剪
const handleCancel = () => {
  cropDialogVisible.value = false;
  emit("crop-cancel");
};

// 对话框关闭处理
const handleDialogClose = () => {
  // 清理资源
  if (canvasContext) {
    canvasContext.clearRect(
      0,
      0,
      cropCanvasRef.value.width,
      cropCanvasRef.value.height
    );
    canvasContext = null;
  }

  originalImage = null;

  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
    originalImageUrl.value = "";
  }

  // 重置状态
  Object.assign(cropState, {
    isDragging: false,
    isResizing: false,
    resizeType: "",
    startX: 0,
    startY: 0,
    cropX: 50,
    cropY: 50,
    cropWidth: 200,
    cropHeight: 200,
    scale: 1,
    rotation: 0,
  });
};

// 鼠标事件处理
const startCrop = (event) => {
  event.preventDefault();

  const canvas = cropCanvasRef.value;
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  // 检查是否点击在裁剪框内
  const { cropX, cropY, cropWidth, cropHeight } = cropState;

  if (x >= cropX && x <= cropX + cropWidth && y >= cropY && y <= cropY + cropHeight) {
    cropState.isDragging = true;
    cropState.startX = x - cropX;
    cropState.startY = y - cropY;
  }
};

const updateCrop = (event) => {
  if (!cropState.isDragging) return;

  event.preventDefault();

  const canvas = cropCanvasRef.value;
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  // 更新裁剪框位置
  const newX = x - cropState.startX;
  const newY = y - cropState.startY;

  // 限制在canvas范围内
  cropState.cropX = Math.max(0, Math.min(newX, canvas.width - cropState.cropWidth));
  cropState.cropY = Math.max(0, Math.min(newY, canvas.height - cropState.cropHeight));

  drawCanvas();
  updatePreviews();
};

const endCrop = (event) => {
  cropState.isDragging = false;
};

// 组件卸载时清理
onBeforeUnmount(() => {
  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
  }

  if (currentAvatar.value && currentAvatar.value.startsWith("blob:")) {
    URL.revokeObjectURL(currentAvatar.value);
  }
});
</script>

<style scoped>
.modern-avatar-uploader {
  display: inline-block;
}

.avatar-display {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.avatar-display:hover {
  transform: translateY(-2px);
}

.avatar-container {
  position: relative;
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #ffffff;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar-container:hover {
  border-color: #409eff;
  box-shadow:
    0 8px 25px rgba(64, 158, 255, 0.2),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
}

.placeholder-text {
  margin-top: 8px;
  text-align: center;
  line-height: 1.2;
  font-weight: 400;
  opacity: 0.8;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(64, 158, 255, 0.95) 0%,
    rgba(103, 194, 58, 0.95) 100%
  );
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  backdrop-filter: blur(4px);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.overlay-text {
  margin-top: 4px;
  text-align: center;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 现代化对话框样式 */
.modern-crop-dialog {
  --el-dialog-padding-primary: 0;
}

.modern-crop-dialog :deep(.el-dialog) {
  margin: 2vh auto;
  border-radius: 16px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1);
  max-width: 95vw;
  max-height: 90vh;
  overflow: hidden;
}

.modern-crop-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 0;
  border-bottom: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modern-crop-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.modern-crop-dialog :deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
}

.modern-crop-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.modern-crop-dialog :deep(.el-dialog__body) {
  padding: 0;
  height: 600px;
  overflow: hidden;
  background: #f8fafc;
}

.crop-container {
  display: grid;
  grid-template-columns: 1.8fr 1fr;
  height: 100%;
  min-height: 600px;
}

.crop-section {
  display: flex;
  flex-direction: column;
  background: white;
  border-right: 1px solid #e2e8f0;
}

.crop-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.crop-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.crop-header p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.crop-area {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.crop-canvas {
  display: block;
  cursor: move;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  max-height: 100%;
}

.crop-tools {
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #f1f5f9;
  display: flex;
  gap: 24px;
  align-items: center;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.tool-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  min-width: 40px;
}

.scale-slider {
  flex: 1;
}

.rotation-controls {
  display: flex;
  gap: 8px;
}

.preview-section {
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.preview-header {
  padding: 24px 24px 16px;
  background: white;
  border-bottom: 1px solid #f1f5f9;
}

.preview-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.preview-header p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.preview-grid {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.preview-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.preview-circle {
  border-radius: 50%;
  border: 2px solid #e2e8f0;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-circle.large {
  width: 120px;
  height: 120px;
}

.preview-circle.medium {
  width: 80px;
  height: 80px;
}

.preview-circle.small {
  width: 40px;
  height: 40px;
}

.preview-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.preview-size {
  font-size: 12px;
  color: #6b7280;
}

.quality-settings {
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #f1f5f9;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.setting-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #e2e8f0;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .modern-crop-dialog :deep(.el-dialog) {
    width: 95vw !important;
    margin: 1vh auto;
  }

  .crop-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
    height: auto;
  }

  .modern-crop-dialog :deep(.el-dialog__body) {
    height: auto;
    max-height: 85vh;
    overflow-y: auto;
  }

  .crop-section {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  .crop-area {
    height: 300px;
  }

  .preview-grid {
    flex-direction: row;
    justify-content: center;
    padding: 16px 24px;
  }

  .preview-item {
    flex: 1;
    max-width: 140px;
  }

  .crop-tools {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .tool-group {
    justify-content: space-between;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .footer-actions {
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .modern-crop-dialog :deep(.el-dialog__header) {
    padding: 16px;
  }

  .crop-header,
  .preview-header {
    padding: 16px;
  }

  .crop-area {
    padding: 16px;
    height: 250px;
  }

  .crop-tools {
    padding: 16px;
  }

  .preview-grid {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .quality-settings {
    padding: 16px;
  }

  .dialog-footer {
    padding: 16px;
  }

  .footer-actions .el-button {
    flex: 1;
  }
}

/* 禁用状态 */
.modern-avatar-uploader.disabled .avatar-display {
  cursor: not-allowed;
  opacity: 0.6;
}

.modern-avatar-uploader.disabled .avatar-container:hover {
  transform: none;
  border-color: #e4e7ed;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modern-avatar-uploader.disabled .avatar-overlay {
  display: none;
}
</style>
