<template>
  <div class="avatar-demo-page">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">现代化头像上传组件</h1>
        <p class="page-description">
          全新设计的头像上传组件，支持拖拽裁剪、实时预览、多种输出格式
        </p>
      </div>

      <!-- 功能展示区域 -->
      <div class="demo-grid">
        <!-- 基础用法 -->
        <div class="demo-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><User /></el-icon>
              基础用法
            </h3>
            <p class="card-description">标准的头像上传功能</p>
          </div>
          
          <div class="card-content">
            <div class="demo-item">
              <ModernAvatarUploader
                v-model="basicAvatar"
                :avatar-size="100"
                placeholder-text="点击上传头像"
                overlay-text="更换头像"
                @crop-complete="handleBasicUpload"
              />
              
              <div class="demo-info">
                <p><strong>尺寸:</strong> 100×100px</p>
                <p><strong>格式:</strong> JPEG</p>
                <p><strong>质量:</strong> 90%</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 大尺寸头像 -->
        <div class="demo-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Picture /></el-icon>
              大尺寸头像
            </h3>
            <p class="card-description">适用于个人资料页面</p>
          </div>
          
          <div class="card-content">
            <div class="demo-item">
              <ModernAvatarUploader
                v-model="largeAvatar"
                :avatar-size="150"
                :output-size="300"
                placeholder-text="上传大头像"
                overlay-text="更换大头像"
                @crop-complete="handleLargeUpload"
              />
              
              <div class="demo-info">
                <p><strong>尺寸:</strong> 150×150px</p>
                <p><strong>输出:</strong> 300×300px</p>
                <p><strong>用途:</strong> 个人资料</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 小尺寸头像 -->
        <div class="demo-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Avatar /></el-icon>
              小尺寸头像
            </h3>
            <p class="card-description">适用于列表和评论</p>
          </div>
          
          <div class="card-content">
            <div class="demo-item">
              <ModernAvatarUploader
                v-model="smallAvatar"
                :avatar-size="60"
                :output-size="120"
                placeholder-text="小头像"
                overlay-text="更换"
                @crop-complete="handleSmallUpload"
              />
              
              <div class="demo-info">
                <p><strong>尺寸:</strong> 60×60px</p>
                <p><strong>输出:</strong> 120×120px</p>
                <p><strong>用途:</strong> 列表显示</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 高质量头像 -->
        <div class="demo-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon><Star /></el-icon>
              高质量头像
            </h3>
            <p class="card-description">PNG格式，无损压缩</p>
          </div>
          
          <div class="card-content">
            <div class="demo-item">
              <ModernAvatarUploader
                v-model="hqAvatar"
                :avatar-size="120"
                :output-size="400"
                :output-quality="1.0"
                output-format="image/png"
                placeholder-text="高质量头像"
                overlay-text="上传PNG"
                @crop-complete="handleHQUpload"
              />
              
              <div class="demo-info">
                <p><strong>尺寸:</strong> 120×120px</p>
                <p><strong>输出:</strong> 400×400px</p>
                <p><strong>格式:</strong> PNG</p>
                <p><strong>质量:</strong> 100%</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能特性 -->
      <div class="features-section">
        <h2 class="section-title">功能特性</h2>
        
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="24"><Crop /></el-icon>
            </div>
            <h4 class="feature-title">智能裁剪</h4>
            <p class="feature-description">
              支持拖拽调整裁剪区域，实时预览裁剪效果，确保最佳显示效果
            </p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="24"><ZoomIn /></el-icon>
            </div>
            <h4 class="feature-title">缩放旋转</h4>
            <p class="feature-description">
              支持图片缩放和旋转，灵活调整图片角度和大小
            </p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="24"><View /></el-icon>
            </div>
            <h4 class="feature-title">实时预览</h4>
            <p class="feature-description">
              提供多种尺寸的实时预览，直观查看不同场景下的显示效果
            </p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="24"><Setting /></el-icon>
            </div>
            <h4 class="feature-title">灵活配置</h4>
            <p class="feature-description">
              支持多种输出格式和质量设置，满足不同场景需求
            </p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="24"><Mobile /></el-icon>
            </div>
            <h4 class="feature-title">响应式设计</h4>
            <p class="feature-description">
              完美适配各种屏幕尺寸，移动端和桌面端都有优秀体验
            </p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon size="24"><Check /></el-icon>
            </div>
            <h4 class="feature-title">类型验证</h4>
            <p class="feature-description">
              自动验证文件类型和大小，确保上传文件的安全性
            </p>
          </div>
        </div>
      </div>

      <!-- 使用统计 -->
      <div class="stats-section">
        <h2 class="section-title">使用统计</h2>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ uploadCount }}</div>
            <div class="stat-label">总上传次数</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-number">{{ formatFileSize(totalSize) }}</div>
            <div class="stat-label">总文件大小</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-number">{{ avgQuality }}%</div>
            <div class="stat-label">平均质量</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-number">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  User,
  Picture,
  Avatar,
  Star,
  Crop,
  ZoomIn,
  View,
  Setting,
  Mobile,
  Check,
} from "@element-plus/icons-vue";

import ModernAvatarUploader from "@/components/common/ModernAvatarUploader.vue";

// 头像数据
const basicAvatar = ref("");
const largeAvatar = ref("");
const smallAvatar = ref("");
const hqAvatar = ref("");

// 统计数据
const uploadCount = ref(0);
const totalSize = ref(0);
const totalQuality = ref(0);
const successCount = ref(0);

// 计算属性
const avgQuality = computed(() => {
  return uploadCount.value > 0 ? Math.round(totalQuality.value / uploadCount.value * 100) : 0;
});

const successRate = computed(() => {
  return uploadCount.value > 0 ? Math.round(successCount.value / uploadCount.value * 100) : 100;
});

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";
  
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 上传处理函数
const handleBasicUpload = (data) => {
  updateStats(data, 0.9);
  ElMessage.success("基础头像上传成功！");
};

const handleLargeUpload = (data) => {
  updateStats(data, 0.9);
  ElMessage.success("大尺寸头像上传成功！");
};

const handleSmallUpload = (data) => {
  updateStats(data, 0.9);
  ElMessage.success("小尺寸头像上传成功！");
};

const handleHQUpload = (data) => {
  updateStats(data, 1.0);
  ElMessage.success("高质量头像上传成功！");
};

// 更新统计数据
const updateStats = (data, quality) => {
  uploadCount.value++;
  totalSize.value += data.size;
  totalQuality.value += quality;
  successCount.value++;
};
</script>

<style scoped>
.avatar-demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 60px;
  color: white;
}

.page-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 20px;
  margin: 0;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 80px;
}

.demo-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.demo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.card-description {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

.card-content {
  padding: 32px 24px;
}

.demo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.demo-info {
  text-align: center;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

.demo-info p {
  margin: 4px 0;
}

.features-section,
.stats-section {
  margin-bottom: 60px;
}

.section-title {
  text-align: center;
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin: 0 0 40px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.feature-item {
  background: white;
  padding: 32px 24px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.feature-description {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.stat-item {
  background: white;
  padding: 32px 24px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.stat-label {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .avatar-demo-page {
    padding: 20px 16px;
  }

  .page-title {
    font-size: 32px;
  }

  .page-description {
    font-size: 16px;
  }

  .demo-grid,
  .features-grid,
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .section-title {
    font-size: 28px;
  }

  .card-header,
  .card-content {
    padding: 20px;
  }

  .feature-item,
  .stat-item {
    padding: 24px 20px;
  }
}
</style>
